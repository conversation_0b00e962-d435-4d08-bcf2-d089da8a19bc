<script setup lang="ts">
import { Search } from '@element-plus/icons-vue'
</script>

<template>
  <div class="search">
    <el-autocomplete
      clearable
      placeholder="请输入医院名称"
      class="input" />
    <el-button type="primary" :icon="Search"></el-button>
  </div>
</template>

<style scoped lang="scss">
.search {
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10px 0;
  :deep(.el-autocomplete) {
    width: 600px;
    margin-right: 10px;
    .el-input {
      height: 50px;
      font-size: 16px;
    }
  }

  :deep(.el-button) {
    height: 50px;
    width: 50px;
    .el-icon {
      font-size: 20px;
    }
  }
}
</style>
