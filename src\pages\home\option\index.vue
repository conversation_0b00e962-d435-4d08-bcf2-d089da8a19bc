<script setup lang="ts">
defineProps(['name', 'options'])
</script>

<template>
  <div class="content">
    <span>{{ name }}:</span>
    <ul>
      <li class="active">全部</li>
      <li v-for="option in options" :key="option">{{ option }}</li>
    </ul>
  </div>
</template>

<style scoped lang="scss">
.content {
  display: flex;
  align-items: baseline;
  color: #7f7f7f;

  span {
    margin-right: 10px;
    flex-shrink: 0;
  }

  ul {
    padding-left: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    list-style-type: none;
    user-select: none;

    li {
      cursor: pointer;
      &.active {
        color: #55a6fe;
      }

      &:hover {
        color: #55a6fe;
      }
    }
  }
}
</style>